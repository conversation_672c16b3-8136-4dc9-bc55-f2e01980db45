package com.chic.dea.domain.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chic.dea.apis.model.dto.LineageTableFieldsVO;
import com.chic.dea.domain.database.mapper.SensitiveFieldMapper;
import com.chic.dea.domain.service.SensitiveFieldService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 敏感字段检查服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
public class SensitiveFieldServiceImpl implements SensitiveFieldService {

    @Autowired
    private SensitiveFieldMapper sensitiveFieldMapper;

    @Override
    public List<SensitiveFieldCheckResult> checkSensitiveFields(List<LineageTableFieldsVO> list) {
        log.info("查询敏感字段参数: {}", list.toString());


        
        return null;
    }








}
