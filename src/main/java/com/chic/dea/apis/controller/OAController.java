package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.OAInfoResponse;
import com.chic.dea.domain.service.OAService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * OA信息查询控制器
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Slf4j
@RestController
@RequestMapping("/api/oa")
@RequiredArgsConstructor
public class OAController {

    private final OAService oaService;

    /**
     * 根据ID查询OA信息
     * 
     * @param oaId OA ID
     * @return OA信息
     */
    @GetMapping("/{oaId}")
    public Result<OAInfoResponse> getOAInfo(@PathVariable String oaId) {
        try {
            log.info("查询OA信息，ID: {}", oaId);
            OAInfoResponse oaInfo = oaService.getOAInfoById(oaId);
            
            if (Boolean.TRUE.equals(oaInfo.getSuccess())) {
                log.info("查询OA信息成功，ID: {}, 标题: {}", oaId, oaInfo.getBt());
                return Result.success(oaInfo);
            } else {
                log.warn("查询OA信息失败，ID: {}, 错误: {}", oaId, oaInfo.getErrorMessage());
                return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), oaInfo.getErrorMessage()));
            }
        } catch (Exception e) {
            log.error("查询OA信息异常，ID: {}", oaId, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询OA信息失败: " + e.getMessage()));
        }
    }

    /**
     * 查询OA信息（用于任务创建时的OA信息回填）
     * 根据设计文档3.4.1中的业务流程，这是新建OA提数任务时使用的接口
     * 
     * @param oaId OA ID
     * @return OA信息
     */
    @PostMapping("/query")
    public Result<OAInfoResponse> queryOAInfo(@RequestParam String oaId) {
        try {
            log.info("查询OA信息（任务创建），ID: {}", oaId);
            OAInfoResponse oaInfo = oaService.getOAInfoById(oaId);
            
            if (Boolean.TRUE.equals(oaInfo.getSuccess())) {
                log.info("查询OA信息成功（任务创建），ID: {}, 标题: {}", oaId, oaInfo.getBt());
                return Result.success(oaInfo);
            } else {
                log.warn("查询OA信息失败（任务创建），ID: {}, 错误: {}", oaId, oaInfo.getErrorMessage());
                return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), oaInfo.getErrorMessage()));
            }
        } catch (Exception e) {
            log.error("查询OA信息异常（任务创建），ID: {}", oaId, e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询OA信息失败: " + e.getMessage()));
        }
    }
}